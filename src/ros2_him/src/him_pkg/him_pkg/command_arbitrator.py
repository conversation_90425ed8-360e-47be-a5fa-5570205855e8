#!/usr/bin/env python3
###################################################################
# Copyright (C) 2025 Vita Dynamics. All rights reserved.
#
# Filename: command_arbitrator.py
# Author : AI Assistant
# Date : Jan, 2025
# Describe: 命令仲裁器，确保同时只有一个节点在发送速度指令
###################################################################

import time
import threading
from typing import Optional, Dict, Any

import rclpy
from rclpy.node import Node
from rclpy.executors import SingleThreadedExecutor
from geometry_msgs.msg import Twist
from sensor_msgs.msg import Joy
from std_msgs.msg import Bool, String


class CommandArbitrator(Node):
    """
    命令仲裁器
    功能：
    1. 监听多个速度指令源
    2. 根据优先级决定哪个指令生效
    3. 确保同时只有一个节点在控制机器人
    4. 提供平滑的切换机制
    """
    
    def __init__(self):
        super().__init__('command_arbitrator')
        
        # 命令源优先级 (数字越小优先级越高)
        self.command_priorities = {
            'emergency_stop': 0,    # 紧急停止
            'manual_joy': 1,        # 手动joy控制
            'external_cmd': 2,      # 外部命令
            'script_cmd': 3,        # 脚本命令
            'default': 999          # 默认（无命令）
        }
        
        # 当前活跃的命令源
        self.active_source = 'default'
        self.last_command_time = {}
        self.command_timeout = 1.0  # 1秒超时
        
        # 最后接收到的命令
        self.last_commands = {}
        
        # 创建订阅者
        self.joy_sub = self.create_subscription(
            Joy, '/joy', self.joy_callback, 10
        )
        
        self.external_cmd_sub = self.create_subscription(
            Twist, '/external_cmd', self.external_cmd_callback, 10
        )
        
        self.script_cmd_sub = self.create_subscription(
            Twist, '/script_cmd', self.script_cmd_callback, 10
        )
        
        self.emergency_stop_sub = self.create_subscription(
            Bool, '/emergency_stop', self.emergency_stop_callback, 10
        )
        
        # 创建发布者
        self.vel_cmd_pub = self.create_publisher(Twist, '/vel_cmd', 10)
        self.status_pub = self.create_publisher(String, '/arbitrator_status', 10)
        
        # 创建定时器
        self.arbitration_timer = self.create_timer(0.05, self.arbitrate_commands)  # 20Hz
        self.status_timer = self.create_timer(1.0, self.publish_status)  # 1Hz
        
        # 状态变量
        self.emergency_stop_active = False
        self.lock = threading.Lock()
        
        self.get_logger().info("Command Arbitrator initialized")
        
    def joy_callback(self, msg: Joy):
        """处理joy输入"""
        with self.lock:
            # 检查是否有有效输入
            has_input = False
            if len(msg.axes) >= 4:
                lx, ly, rx, ry = msg.axes[0], msg.axes[1], msg.axes[3], msg.axes[4]
                if abs(lx) > 0.1 or abs(ly) > 0.1 or abs(rx) > 0.1 or abs(ry) > 0.1:
                    has_input = True
            
            if len(msg.buttons) > 0 and any(button > 0 for button in msg.buttons):
                has_input = True
            
            if has_input:
                # 转换为Twist消息
                cmd = Twist()
                if len(msg.axes) >= 4:
                    cmd.linear.x = msg.axes[1] * 0.8  # ly * scale
                    cmd.linear.y = msg.axes[0] * 0.0  # lx * scale (通常为0)
                    cmd.angular.z = msg.axes[3] * 1.0  # rx * scale
                
                self.last_commands['manual_joy'] = cmd
                self.last_command_time['manual_joy'] = time.time()
    
    def external_cmd_callback(self, msg: Twist):
        """处理外部命令"""
        with self.lock:
            self.last_commands['external_cmd'] = msg
            self.last_command_time['external_cmd'] = time.time()
    
    def script_cmd_callback(self, msg: Twist):
        """处理脚本命令"""
        with self.lock:
            self.last_commands['script_cmd'] = msg
            self.last_command_time['script_cmd'] = time.time()
    
    def emergency_stop_callback(self, msg: Bool):
        """处理紧急停止"""
        with self.lock:
            self.emergency_stop_active = msg.data
            if msg.data:
                stop_cmd = Twist()
                self.last_commands['emergency_stop'] = stop_cmd
                self.last_command_time['emergency_stop'] = time.time()
                self.get_logger().warn("Emergency stop activated!")
            else:
                self.get_logger().info("Emergency stop deactivated")
    
    def arbitrate_commands(self):
        """仲裁命令"""
        with self.lock:
            current_time = time.time()
            
            # 清理超时的命令
            expired_sources = []
            for source, last_time in self.last_command_time.items():
                if current_time - last_time > self.command_timeout:
                    expired_sources.append(source)
            
            for source in expired_sources:
                if source in self.last_commands:
                    del self.last_commands[source]
                del self.last_command_time[source]
            
            # 选择最高优先级的活跃命令源
            active_sources = list(self.last_commands.keys())
            if not active_sources:
                new_active_source = 'default'
                cmd_to_send = Twist()  # 零速度
            else:
                # 按优先级排序
                active_sources.sort(key=lambda x: self.command_priorities.get(x, 999))
                new_active_source = active_sources[0]
                cmd_to_send = self.last_commands[new_active_source]
            
            # 检查是否需要切换命令源
            if new_active_source != self.active_source:
                self.get_logger().info(f"Switching command source: {self.active_source} -> {new_active_source}")
                self.active_source = new_active_source
            
            # 发布命令
            self.vel_cmd_pub.publish(cmd_to_send)
    
    def publish_status(self):
        """发布状态信息"""
        with self.lock:
            status_msg = String()
            status_info = {
                'active_source': self.active_source,
                'available_sources': list(self.last_commands.keys()),
                'emergency_stop': self.emergency_stop_active
            }
            status_msg.data = str(status_info)
            self.status_pub.publish(status_msg)


class ScriptCommandPublisher(Node):
    """脚本命令发布器"""
    
    def __init__(self):
        super().__init__('script_command_publisher')
        
        self.cmd_pub = self.create_publisher(Twist, '/script_cmd', 10)
        self.timer = None
        self.target_cmd = Twist()
        
    def start_publishing(self, linear_x=0.0, linear_y=0.0, angular_z=0.0, duration=None):
        """开始发布命令"""
        self.target_cmd.linear.x = linear_x
        self.target_cmd.linear.y = linear_y
        self.target_cmd.angular.z = angular_z
        
        # 创建定时器，20Hz发布
        self.timer = self.create_timer(0.05, self.publish_command)
        
        self.get_logger().info(
            f"Started publishing script commands: "
            f"linear=({linear_x:.2f}, {linear_y:.2f}), angular={angular_z:.2f}"
        )
        
        if duration:
            # 设置停止定时器
            self.stop_timer = self.create_timer(duration, self.stop_publishing)
    
    def publish_command(self):
        """发布命令"""
        self.cmd_pub.publish(self.target_cmd)
    
    def stop_publishing(self):
        """停止发布命令"""
        if self.timer:
            self.timer.destroy()
            self.timer = None
        
        if hasattr(self, 'stop_timer'):
            self.stop_timer.destroy()
        
        # 发送停止命令
        stop_cmd = Twist()
        self.cmd_pub.publish(stop_cmd)
        
        self.get_logger().info("Stopped publishing script commands")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Command Arbitrator')
    parser.add_argument('--mode', choices=['arbitrator', 'publisher'], 
                       default='arbitrator', help='运行模式')
    parser.add_argument('--linear-x', type=float, default=0.0)
    parser.add_argument('--linear-y', type=float, default=0.0)
    parser.add_argument('--angular-z', type=float, default=0.0)
    parser.add_argument('--duration', type=float, default=None)
    
    args = parser.parse_args()
    
    rclpy.init()
    
    try:
        if args.mode == 'arbitrator':
            node = CommandArbitrator()
            rclpy.spin(node)
        else:
            node = ScriptCommandPublisher()
            node.start_publishing(
                args.linear_x, args.linear_y, args.angular_z, args.duration
            )
            if args.duration:
                rclpy.spin_once(node, timeout_sec=args.duration + 1.0)
            else:
                rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
