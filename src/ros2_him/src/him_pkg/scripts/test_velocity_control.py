#!/usr/bin/env python3
###################################################################
# Copyright (C) 2025 Vita Dynamics. All rights reserved.
#
# Filename: test_velocity_control.py
# Author : AI Assistant
# Date : Jan, 2025
# Describe: 测试速度控制功能
###################################################################

import time
import threading
import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist
from sensor_msgs.msg import Joy


class VelocityControlTester(Node):
    """速度控制测试器"""
    
    def __init__(self):
        super().__init__('velocity_control_tester')
        
        # 创建订阅者监听速度指令
        self.vel_cmd_sub = self.create_subscription(
            Twist,
            '/vel_cmd',
            self.vel_cmd_callback,
            10
        )
        
        self.joy_cmd_sub = self.create_subscription(
            Twist,
            '/joy_cmd',
            self.joy_cmd_callback,
            10
        )
        
        # 创建发布者模拟joy输入
        self.joy_pub = self.create_publisher(Joy, '/joy', 10)
        
        self.received_vel_cmds = []
        self.received_joy_cmds = []
        
        self.get_logger().info("Velocity control tester initialized")
    
    def vel_cmd_callback(self, msg):
        """记录接收到的速度指令"""
        timestamp = time.time()
        self.received_vel_cmds.append((timestamp, msg))
        self.get_logger().info(
            f"Received vel_cmd: linear=({msg.linear.x:.2f}, {msg.linear.y:.2f}), "
            f"angular={msg.angular.z:.2f}"
        )
    
    def joy_cmd_callback(self, msg):
        """记录接收到的joy指令"""
        timestamp = time.time()
        self.received_joy_cmds.append((timestamp, msg))
        if abs(msg.linear.x) > 0.01 or abs(msg.linear.y) > 0.01 or abs(msg.angular.z) > 0.01:
            self.get_logger().info(
                f"Received joy_cmd: linear=({msg.linear.x:.2f}, {msg.linear.y:.2f}), "
                f"angular={msg.angular.z:.2f}"
            )
    
    def simulate_joy_input(self, duration=3.0):
        """模拟joy输入"""
        self.get_logger().info(f"Simulating joy input for {duration} seconds...")
        
        start_time = time.time()
        rate = 20.0  # 20Hz
        period = 1.0 / rate
        
        while time.time() - start_time < duration:
            joy_msg = Joy()
            joy_msg.header.stamp = self.get_clock().now().to_msg()
            
            # 模拟摇杆输入
            joy_msg.axes = [0.5, 0.0, 0.0, 0.2, 0.0, 0.0]  # lx, ly, ?, rx, ry, ?
            joy_msg.buttons = [0] * 10  # 10个按钮都不按
            
            self.joy_pub.publish(joy_msg)
            time.sleep(period)
        
        self.get_logger().info("Joy input simulation completed")
    
    def get_statistics(self):
        """获取统计信息"""
        vel_cmd_count = len(self.received_vel_cmds)
        joy_cmd_count = len([cmd for _, cmd in self.received_joy_cmds 
                           if abs(cmd.linear.x) > 0.01 or abs(cmd.linear.y) > 0.01 or abs(cmd.angular.z) > 0.01])
        
        return {
            'vel_cmd_count': vel_cmd_count,
            'joy_cmd_count': joy_cmd_count,
            'total_vel_cmds': self.received_vel_cmds,
            'total_joy_cmds': self.received_joy_cmds
        }


def test_simple_mode():
    """测试简单模式"""
    print("\n=== Testing Simple Mode ===")
    
    rclpy.init()
    tester = VelocityControlTester()
    
    # 启动测试节点
    executor = rclpy.executors.SingleThreadedExecutor()
    executor.add_node(tester)
    
    spin_thread = threading.Thread(target=executor.spin)
    spin_thread.start()
    
    try:
        # 等待节点初始化
        time.sleep(1.0)
        
        print("Starting simple velocity commander...")
        import subprocess
        import os
        
        # 获取脚本路径
        script_dir = os.path.dirname(os.path.abspath(__file__))
        commander_script = os.path.join(script_dir, 'velocity_commander.py')
        
        # 启动简单速度指令器
        process = subprocess.Popen([
            'python3', commander_script,
            '--velocity', '0.5,0.0,0.2',
            '--duration', '3.0'
        ])
        
        # 等待完成
        process.wait()
        
        # 等待一点时间收集数据
        time.sleep(1.0)
        
        # 获取统计信息
        stats = tester.get_statistics()
        print(f"Received {stats['vel_cmd_count']} vel_cmd messages")
        print(f"Received {stats['joy_cmd_count']} non-zero joy_cmd messages")
        
        if stats['vel_cmd_count'] > 0:
            print("✓ Simple mode test PASSED")
        else:
            print("✗ Simple mode test FAILED")
            
    except Exception as e:
        print(f"Error in simple mode test: {e}")
    finally:
        executor.shutdown()
        spin_thread.join()
        rclpy.shutdown()


def test_smart_mode():
    """测试智能模式"""
    print("\n=== Testing Smart Mode ===")
    
    rclpy.init()
    tester = VelocityControlTester()
    
    # 启动测试节点
    executor = rclpy.executors.SingleThreadedExecutor()
    executor.add_node(tester)
    
    spin_thread = threading.Thread(target=executor.spin)
    spin_thread.start()
    
    try:
        # 等待节点初始化
        time.sleep(1.0)
        
        print("Testing conflict detection...")
        
        # 模拟joy输入
        joy_thread = threading.Thread(target=tester.simulate_joy_input, args=(3.0,))
        joy_thread.start()
        
        # 等待joy模拟完成
        joy_thread.join()
        
        # 等待一点时间
        time.sleep(1.0)
        
        # 获取统计信息
        stats = tester.get_statistics()
        print(f"During joy simulation: received {stats['joy_cmd_count']} joy_cmd messages")
        
        if stats['joy_cmd_count'] > 0:
            print("✓ Joy simulation test PASSED")
        else:
            print("✗ Joy simulation test FAILED")
            
    except Exception as e:
        print(f"Error in smart mode test: {e}")
    finally:
        executor.shutdown()
        spin_thread.join()
        rclpy.shutdown()


def main():
    """主测试函数"""
    print("Starting velocity control tests...")
    
    try:
        # 测试简单模式
        test_simple_mode()
        
        # 等待一下
        time.sleep(2.0)
        
        # 测试智能模式
        test_smart_mode()
        
        print("\n=== Test Summary ===")
        print("All tests completed. Check the output above for results.")
        
    except KeyboardInterrupt:
        print("\nTests interrupted by user")
    except Exception as e:
        print(f"Test error: {e}")


if __name__ == '__main__':
    main()
