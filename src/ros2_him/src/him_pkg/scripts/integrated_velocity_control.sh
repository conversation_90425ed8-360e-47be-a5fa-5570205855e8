#!/bin/bash
###################################################################
# Copyright (C) 2025 Vita Dynamics. All rights reserved.
#
# Filename: integrated_velocity_control.sh
# Author : AI Assistant
# Date : Jan, 2025
# Describe: 集成的速度控制脚本，使用命令仲裁器确保无冲突
###################################################################

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PACKAGE_DIR="$(dirname "$SCRIPT_DIR")"

# 默认参数
LINEAR_X=0.0
LINEAR_Y=0.0
ANGULAR_Z=0.0
DURATION=10.0
START_ARBITRATOR=true

# 显示帮助信息
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "集成速度控制脚本 - 使用命令仲裁器确保无冲突"
    echo ""
    echo "Options:"
    echo "  -x, --linear-x FLOAT    Linear velocity in x direction (m/s) [default: 0.0]"
    echo "  -y, --linear-y FLOAT    Linear velocity in y direction (m/s) [default: 0.0]"
    echo "  -z, --angular-z FLOAT   Angular velocity around z axis (rad/s) [default: 0.0]"
    echo "  -d, --duration FLOAT    Duration to run in seconds [default: 10.0]"
    echo "  --no-arbitrator         Don't start command arbitrator (assume it's already running)"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 -x 0.5 -d 5.0                    # Move forward at 0.5 m/s for 5 seconds"
    echo "  $0 -x 0.3 -z 0.2 -d 10.0           # Move forward and turn for 10 seconds"
    echo "  $0 --no-arbitrator -x 0.5 -d 30.0  # Use existing arbitrator"
    echo ""
    echo "Features:"
    echo "  - Automatic conflict detection with external joy controllers"
    echo "  - Priority-based command arbitration"
    echo "  - Graceful shutdown with stop commands"
    echo "  - Real-time status monitoring"
    echo ""
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -x|--linear-x)
            LINEAR_X="$2"
            shift 2
            ;;
        -y|--linear-y)
            LINEAR_Y="$2"
            shift 2
            ;;
        -z|--angular-z)
            ANGULAR_Z="$2"
            shift 2
            ;;
        -d|--duration)
            DURATION="$2"
            shift 2
            ;;
        --no-arbitrator)
            START_ARBITRATOR=false
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证参数
if ! [[ "$LINEAR_X" =~ ^-?[0-9]+\.?[0-9]*$ ]]; then
    echo "Error: Invalid linear-x value: $LINEAR_X"
    exit 1
fi

if ! [[ "$LINEAR_Y" =~ ^-?[0-9]+\.?[0-9]*$ ]]; then
    echo "Error: Invalid linear-y value: $LINEAR_Y"
    exit 1
fi

if ! [[ "$ANGULAR_Z" =~ ^-?[0-9]+\.?[0-9]*$ ]]; then
    echo "Error: Invalid angular-z value: $ANGULAR_Z"
    exit 1
fi

if ! [[ "$DURATION" =~ ^[0-9]+\.?[0-9]*$ ]]; then
    echo "Error: Invalid duration value: $DURATION"
    exit 1
fi

# 检查ROS2环境
if ! command -v ros2 &> /dev/null; then
    echo "Error: ROS2 not found. Please source your ROS2 environment."
    exit 1
fi

# 显示配置信息
echo "=== 集成速度控制配置 ==="
echo "线速度: ($LINEAR_X, $LINEAR_Y) m/s"
echo "角速度: $ANGULAR_Z rad/s"
echo "持续时间: $DURATION 秒"
echo "启动仲裁器: $START_ARBITRATOR"
echo "=========================="

# 清理函数
cleanup() {
    echo ""
    echo "正在停止速度控制..."
    
    # 发送紧急停止信号
    ros2 topic pub --once /emergency_stop std_msgs/msg/Bool "{data: true}" > /dev/null 2>&1
    sleep 0.5
    ros2 topic pub --once /emergency_stop std_msgs/msg/Bool "{data: false}" > /dev/null 2>&1
    
    # 杀死后台进程
    if [[ -n "$ARBITRATOR_PID" ]]; then
        kill $ARBITRATOR_PID 2>/dev/null
        wait $ARBITRATOR_PID 2>/dev/null
    fi
    
    if [[ -n "$PUBLISHER_PID" ]]; then
        kill $PUBLISHER_PID 2>/dev/null
        wait $PUBLISHER_PID 2>/dev/null
    fi
    
    echo "速度控制已停止"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 启动命令仲裁器（如果需要）
if [[ "$START_ARBITRATOR" == "true" ]]; then
    echo "启动命令仲裁器..."
    ros2 run him_pkg command_arbitrator --mode arbitrator &
    ARBITRATOR_PID=$!
    
    # 等待仲裁器启动
    sleep 2.0
    
    # 检查仲裁器是否成功启动
    if ! kill -0 $ARBITRATOR_PID 2>/dev/null; then
        echo "Error: Failed to start command arbitrator"
        exit 1
    fi
    
    echo "命令仲裁器已启动 (PID: $ARBITRATOR_PID)"
fi

# 启动脚本命令发布器
echo "启动脚本命令发布器..."
ros2 run him_pkg command_arbitrator --mode publisher \
    --linear-x "$LINEAR_X" \
    --linear-y "$LINEAR_Y" \
    --angular-z "$ANGULAR_Z" \
    --duration "$DURATION" &
PUBLISHER_PID=$!

echo "脚本命令发布器已启动 (PID: $PUBLISHER_PID)"

# 监控状态
echo "监控命令仲裁器状态..."
echo "按 Ctrl+C 停止"

# 等待发布器完成
wait $PUBLISHER_PID

echo "脚本命令发布完成"

# 清理
cleanup
