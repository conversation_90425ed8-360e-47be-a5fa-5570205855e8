#!/bin/bash
###################################################################
# Copyright (C) 2025 Vita Dynamics. All rights reserved.
#
# Filename: start_velocity_control.sh
# Author : AI Assistant
# Date : Jan, 2025
# Describe: 启动速度控制脚本
###################################################################

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PACKAGE_DIR="$(dirname "$SCRIPT_DIR")"

# 默认参数
LINEAR_X=0.0
LINEAR_Y=0.0
ANGULAR_Z=0.0
DURATION=10.0
MODE="simple"

# 显示帮助信息
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -x, --linear-x FLOAT    Linear velocity in x direction (m/s) [default: 0.0]"
    echo "  -y, --linear-y FLOAT    Linear velocity in y direction (m/s) [default: 0.0]"
    echo "  -z, --angular-z FLOAT   Angular velocity around z axis (rad/s) [default: 0.0]"
    echo "  -d, --duration FLOAT    Duration to run in seconds [default: 10.0]"
    echo "  -m, --mode MODE         Control mode: 'simple' or 'smart' [default: simple]"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 -x 0.5 -d 5.0                    # Move forward at 0.5 m/s for 5 seconds"
    echo "  $0 -x 0.3 -z 0.2 -d 10.0           # Move forward and turn for 10 seconds"
    echo "  $0 -m smart -x 0.5 -d 30.0         # Use smart mode with conflict detection"
    echo ""
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -x|--linear-x)
            LINEAR_X="$2"
            shift 2
            ;;
        -y|--linear-y)
            LINEAR_Y="$2"
            shift 2
            ;;
        -z|--angular-z)
            ANGULAR_Z="$2"
            shift 2
            ;;
        -d|--duration)
            DURATION="$2"
            shift 2
            ;;
        -m|--mode)
            MODE="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证参数
if ! [[ "$LINEAR_X" =~ ^-?[0-9]+\.?[0-9]*$ ]]; then
    echo "Error: Invalid linear-x value: $LINEAR_X"
    exit 1
fi

if ! [[ "$LINEAR_Y" =~ ^-?[0-9]+\.?[0-9]*$ ]]; then
    echo "Error: Invalid linear-y value: $LINEAR_Y"
    exit 1
fi

if ! [[ "$ANGULAR_Z" =~ ^-?[0-9]+\.?[0-9]*$ ]]; then
    echo "Error: Invalid angular-z value: $ANGULAR_Z"
    exit 1
fi

if ! [[ "$DURATION" =~ ^[0-9]+\.?[0-9]*$ ]]; then
    echo "Error: Invalid duration value: $DURATION"
    exit 1
fi

if [[ "$MODE" != "simple" && "$MODE" != "smart" ]]; then
    echo "Error: Invalid mode: $MODE (must be 'simple' or 'smart')"
    exit 1
fi

# 检查ROS2环境
if ! command -v ros2 &> /dev/null; then
    echo "Error: ROS2 not found. Please source your ROS2 environment."
    exit 1
fi

# 显示配置信息
echo "=== Velocity Control Configuration ==="
echo "Mode: $MODE"
echo "Linear velocity: ($LINEAR_X, $LINEAR_Y) m/s"
echo "Angular velocity: $ANGULAR_Z rad/s"
echo "Duration: $DURATION seconds"
echo "======================================"

# 设置信号处理
cleanup() {
    echo ""
    echo "Stopping velocity control..."
    # 发送停止指令
    ros2 topic pub --once /vel_cmd geometry_msgs/msg/Twist "{linear: {x: 0.0, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: 0.0}}" > /dev/null 2>&1
    exit 0
}

trap cleanup SIGINT SIGTERM

# 根据模式选择实现方式
if [[ "$MODE" == "simple" ]]; then
    echo "Starting simple velocity control..."
    
    # 创建临时Python脚本
    TEMP_SCRIPT=$(mktemp)
    cat > "$TEMP_SCRIPT" << 'EOF'
#!/usr/bin/env python3
import sys
import time
import signal
import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist

class SimpleVelocityPublisher(Node):
    def __init__(self):
        super().__init__('simple_velocity_publisher')
        self.publisher = self.create_publisher(Twist, '/vel_cmd', 10)
        
    def publish_velocity(self, linear_x, linear_y, angular_z, duration, rate=20.0):
        cmd = Twist()
        cmd.linear.x = linear_x
        cmd.linear.y = linear_y
        cmd.angular.z = angular_z
        
        period = 1.0 / rate
        total_iterations = int(duration * rate)
        
        self.get_logger().info(f"Publishing velocity: linear=({linear_x:.2f}, {linear_y:.2f}), angular={angular_z:.2f}")
        
        for i in range(total_iterations):
            if not rclpy.ok():
                break
            self.publisher.publish(cmd)
            time.sleep(period)
            
            if i % int(rate) == 0:
                remaining = duration - (i / rate)
                self.get_logger().info(f"Remaining: {remaining:.1f}s")
        
        # Send stop command
        stop_cmd = Twist()
        self.publisher.publish(stop_cmd)
        self.get_logger().info("Velocity control completed")

def signal_handler(signum, frame):
    rclpy.shutdown()
    sys.exit(0)

def main():
    if len(sys.argv) != 5:
        print("Usage: script.py <linear_x> <linear_y> <angular_z> <duration>")
        sys.exit(1)
    
    linear_x = float(sys.argv[1])
    linear_y = float(sys.argv[2])
    angular_z = float(sys.argv[3])
    duration = float(sys.argv[4])
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    rclpy.init()
    node = SimpleVelocityPublisher()
    
    try:
        node.publish_velocity(linear_x, linear_y, angular_z, duration)
    except Exception as e:
        print(f"Error: {e}")
    finally:
        if rclpy.ok():
            rclpy.shutdown()

if __name__ == '__main__':
    main()
EOF
    
    # 运行临时脚本
    python3 "$TEMP_SCRIPT" "$LINEAR_X" "$LINEAR_Y" "$ANGULAR_Z" "$DURATION"
    
    # 清理临时文件
    rm -f "$TEMP_SCRIPT"
    
else
    echo "Starting smart velocity control..."
    echo "Smart mode requires command arbitrator - using integrated script"
    
    # 使用集成脚本
    exec "$SCRIPT_DIR/integrated_velocity_control.sh" -x "$LINEAR_X" -y "$LINEAR_Y" -z "$ANGULAR_Z" -d "$DURATION"
fi

# 确保发送停止指令
cleanup
