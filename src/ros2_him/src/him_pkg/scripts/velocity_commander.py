#!/usr/bin/env python3
###################################################################
# Copyright (C) 2025 Vita Dynamics. All rights reserved.
#
# Filename: velocity_commander.py
# Author : AI Assistant
# Date : Jan, 2025
# Describe: 简单的速度指令发送工具
###################################################################

import argparse
import signal
import sys
import time

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist


class SimpleVelocityCommander(Node):
    """简单的速度指令发送器"""
    
    def __init__(self):
        super().__init__('simple_velocity_commander')
        
        # 创建发布者
        self.cmd_publisher = self.create_publisher(
            Twist,
            '/vel_cmd',
            10
        )
        
        # 等待发布者准备就绪
        time.sleep(0.5)
        
    def send_velocity(self, linear_x: float, linear_y: float, angular_z: float, 
                     duration: float, rate: float = 20.0):
        """发送指定的速度指令"""
        
        # 创建速度消息
        cmd = Twist()
        cmd.linear.x = linear_x
        cmd.linear.y = linear_y
        cmd.linear.z = 0.0
        cmd.angular.x = 0.0
        cmd.angular.y = 0.0
        cmd.angular.z = angular_z
        
        # 计算发布参数
        period = 1.0 / rate
        total_iterations = int(duration * rate)
        
        self.get_logger().info(
            f"Sending velocity: linear=({linear_x:.2f}, {linear_y:.2f}), "
            f"angular={angular_z:.2f} for {duration:.1f}s at {rate:.1f}Hz"
        )
        
        # 发送速度指令
        for i in range(total_iterations):
            if not rclpy.ok():
                break
                
            self.cmd_publisher.publish(cmd)
            time.sleep(period)
            
            # 每秒打印一次进度
            if i % int(rate) == 0:
                remaining = duration - (i / rate)
                self.get_logger().info(f"Remaining time: {remaining:.1f}s")
        
        # 发送停止指令
        stop_cmd = Twist()
        self.cmd_publisher.publish(stop_cmd)
        self.get_logger().info("Velocity command completed, sent stop command")


def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\nReceived signal {signum}, stopping...")
    rclpy.shutdown()
    sys.exit(0)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Simple Velocity Commander')
    parser.add_argument('--velocity', type=str, required=True,
                       help='Velocity in format "x,y,z" (linear_x,linear_y,angular_z)')
    parser.add_argument('--duration', type=float, required=True,
                       help='Duration to send commands in seconds')
    parser.add_argument('--rate', type=float, default=20.0,
                       help='Publishing rate in Hz (default: 20.0)')
    
    args = parser.parse_args()
    
    # 解析速度参数
    try:
        velocity_parts = args.velocity.split(',')
        if len(velocity_parts) != 3:
            raise ValueError("Velocity must have 3 components")
        
        linear_x = float(velocity_parts[0])
        linear_y = float(velocity_parts[1])
        angular_z = float(velocity_parts[2])
        
    except (ValueError, IndexError) as e:
        print(f"Error parsing velocity: {e}")
        print("Example: --velocity 0.5,0.0,0.2")
        sys.exit(1)
    
    # 设置信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 初始化ROS2
        rclpy.init()
        
        # 创建节点
        commander = SimpleVelocityCommander()
        
        # 发送速度指令
        commander.send_velocity(linear_x, linear_y, angular_z, args.duration, args.rate)
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        if rclpy.ok():
            rclpy.shutdown()


if __name__ == '__main__':
    main()
