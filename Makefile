# Makefile for Vitamin项目

.PHONY: build layout sim msg cfg odom build_sim clean_sim vita00 vita00w vita01 vita02 mac_env vitaflow vitaflow_odom vitaflow_vita_odom build_vitaflow build_odom clip himloco joystick submodule bridge help

# 检测操作系统类型
OS := $(shell uname -s)

# 显示帮助信息
help:
	@echo "可用命令:"
	@echo "  make build  - 使用8个并行工作线程编译整个项目"
	@echo "  make layout - 部署Foxglove布局文件到开发机器"
	@echo "  make sim    - 运行仿真 (在macOS上使用zsh, 在Linux上使用bash)"
	@echo "  make msg    - 编译消息定义"
	@echo "  make cfg    - 配置仿真参数"
	@echo "  make odom   - 运行里程计"
	@echo "  make build_sim - 构建仿真代码"
	@echo "  make clean_sim - 清理仿真代码"
	@echo "  make build_odom - 构建里程计代码"
	@echo "  make vita00 - 更新 vita00 模型"
	@echo "  make vita00w - 更新 vita00w 模型"
	@echo "  make vita01 - 更新 vita01 模型"
	@echo "  make vita02 - 更新 vita02 模型"
	@echo "  make vitaflow ~/Data/test.mcap - 使用指定的mcap文件运行vita_flow"
	@echo "  make vitaflow_odom ~/Data/test.mcap - 使用指定的mcap文件运行vita_flow (使用里程计相关话题配置)"
	@echo "  make vitaflow_vita_odom ~/Data/test.mcap - 使用指定的mcap文件运行vita_flow (使用vita_odom相关话题配置)"
	@echo "  make build_vitaflow - 重新编译vita_flow包以应用更改"
	@echo "  make clip ~/Data/test.mcap 10.5 - 从指定时间点开始裁剪mcap文件"
	@echo "  make mac_env - macOS 进入虚拟 ros 环境"
	@echo "  make himloco - 编译并运行 himloco"
	@echo "  make joystick - 运行 virtual joystick"
	@echo "  make velocity_control x=0.5 y=0.0 z=0.2 d=10.0 m=simple - 速度控制"
	@echo "  make velocity_forward s=0.5 d=5.0 - 向前移动"
	@echo "  make velocity_turn s=0.5 d=5.0 - 原地转弯"
	@echo "  make velocity_smart x=0.5 d=30.0 - 智能速度控制(带冲突检测)"
	@echo "  make submodule - 更新子模块"
	@echo "  make bridge - 启动 foxglove_bridge"
	@echo "  make help   - 显示此帮助信息"

# 整体编译项目
build:
	colcon build --parallel-workers 16 --event-handlers console_direct+ --packages-skip him_pkg

# 部署Foxglove布局文件
layout:
	cd config && make layout

# 运行仿真，根据操作系统和环境选择不同的shell
sim:
	# 检测操作系统和容器环境，使用适当的命令
	@if [ "$(OS)" = "Darwin" ]; then \
		zsh -c "source install/setup.zsh && ros2 run vita_sim vita_mujoco"; \
	elif [ -f /.dockerenv ] || grep -q docker /proc/1/cgroup 2>/dev/null; then \
		bash -c "source install/setup.bash && vglrun ros2 run vita_sim vita_mujoco"; \
	else \
		bash -c "source install/setup.bash && ros2 run vita_sim vita_mujoco"; \
	fi

msg:
	colcon build --parallel-workers 8 --packages-select lowlevel_msg sim_msg --event-handlers console_direct+

# 配置仿真参数
cfg:
	vim install/vita_sim/share/vita_sim/config.yaml

odom:
ifeq ($(OS),Darwin)
	zsh -c "source install/setup.zsh && ros2 launch vita_odom imu_odom.launch.py"
else
	bash -c "source install/setup.bash && ros2 launch vita_odom imu_odom.launch.py"
endif

build_sim:
	colcon build --parallel-workers 8 --packages-select vita_sim --event-handlers console_direct+

clean_sim:
	rm -rf install/vita_sim && cmake --build build/vita_sim --target clean && rm -rf build/vita_sim/CMakeCache.txt

build_vitaflow:
	colcon build --packages-select vita_flow --event-handlers console_direct+

build_odom: msg
	colcon build --parallel-workers 8 --packages-select vita_odom --event-handlers console_direct+

# 更新 vita00 模型（URDF、xml）
vita00:
	colcon build --packages-select vita00 --event-handlers console_direct+

# 更新 vita00w 模型（URDF、xml）
vita00w:
	colcon build --packages-select vita00w --event-handlers console_direct+

vita01:
	colcon build --packages-select vita01 --event-handlers console_direct+

vita02:
	colcon build --packages-select vita02 --event-handlers console_direct+


# 运行vita_flow，支持传递mcap文件路径参数
vitaflow:
	$(eval BAG_PATH := $(filter-out $@,$(MAKECMDGOALS)))
	$(if $(BAG_PATH),,$(error 错误: 需要提供mcap文件路径。用法: make vitaflow ~/Data/test.mcap))
	@echo "使用bag文件: $(BAG_PATH)"
	@echo "存储路径设置为: /tmp"
ifeq ($(OS),Darwin)
	zsh -c "export VITA_STORAGE_PATH='/tmp' && source install/setup.zsh && ros2 launch vita_flow flow.launch.py bag_path:=$(BAG_PATH)"
else
	bash -c "export VITA_STORAGE_PATH='/tmp' && source install/setup.bash && ros2 launch vita_flow flow.launch.py bag_path:=$(BAG_PATH)"
endif

# 运行vita_flow (里程计相关话题)，支持传递mcap文件路径参数
vitaflow_odom:
	$(eval BAG_PATH := $(filter-out $@,$(MAKECMDGOALS)))
	$(if $(BAG_PATH),,$(error 错误: 需要提供mcap文件路径。用法: make vitaflow_odom ~/Data/test.mcap))
	$(eval BAG_DIR := $(dir $(BAG_PATH)))
	@echo "使用bag文件: $(BAG_PATH)"
	@echo "存储路径设置为: $(BAG_DIR)"
	@echo "播放话题: /sportmodestate,/lowstate,/tf,/lio_sam_ros2/mapping/re_location_odometry,/lio_sam/mapping/odometry"
	@echo "录制话题: /sportmodestate,/lowstate,/tf,/lio_sam_ros2/mapping/re_location_odometry,/lio_sam/mapping/odometry,/rt/odom"
ifeq ($(OS),Darwin)
	zsh -c "export VITA_STORAGE_PATH='$(BAG_DIR)' && export VITA_PLAYER_TOPICS='/sportmodestate,/lowstate,/tf,/lio_sam_ros2/mapping/re_location_odometry,/lio_sam/mapping/odometry' && export VITA_RECORDER_TOPICS='/sportmodestate,/lowstate,/tf,/lio_sam_ros2/mapping/re_location_odometry,/lio_sam/mapping/odometry,/rt/odom' && source install/setup.zsh && ros2 launch vita_flow flow.launch.py bag_path:=$(BAG_PATH)"
else
	bash -c "export VITA_STORAGE_PATH='$(BAG_DIR)' && export VITA_PLAYER_TOPICS='/sportmodestate,/lowstate,/tf,/lio_sam_ros2/mapping/re_location_odometry,/lio_sam/mapping/odometry' && export VITA_RECORDER_TOPICS='/sportmodestate,/lowstate,/tf,/lio_sam_ros2/mapping/re_location_odometry,/lio_sam/mapping/odometry,/rt/odom' && source install/setup.bash && ros2 launch vita_flow flow.launch.py bag_path:=$(BAG_PATH)"
endif

vitaflow_vita_odom:
	$(eval BAG_PATH := $(filter-out $@,$(MAKECMDGOALS)))
	$(if $(BAG_PATH),,$(error 错误: 需要提供mcap文件路径。用法: make vitaflow_vita_odom ~/Data/test.mcap))
	$(eval BAG_DIR := $(dir $(BAG_PATH)))
	@echo "使用bag文件: $(BAG_PATH)"
	@echo "存储路径设置为: $(BAG_DIR)"
	@echo "播放话题: /rt/lowstate,/lio_sam/mapping/odometry,/lio_sam/localization/pose_in_map"
	@echo "录制话题: /rt/lowstate,/lio_sam/mapping/odometry,/lio_sam/localization/pose_in_map,/rt/odom,/rt/imu_debug"
ifeq ($(OS),Darwin)
	zsh -c "export VITA_STORAGE_PATH='$(BAG_DIR)' && export VITA_PLAYER_TOPICS='/rt/lowstate,/lio_sam/mapping/odometry,/lio_sam/localization/pose_in_map' && export VITA_RECORDER_TOPICS='/rt/lowstate,/lio_sam/mapping/odometry,/lio_sam/localization/pose_in_map,/rt/odom,/rt/imu_debug' && source install/setup.zsh && ros2 launch vita_flow flow.launch.py bag_path:=$(BAG_PATH)"
else
	bash -c "export VITA_STORAGE_PATH='$(BAG_DIR)' && export VITA_PLAYER_TOPICS='/rt/lowstate,/lio_sam/mapping/odometry,/lio_sam/localization/pose_in_map' && export VITA_RECORDER_TOPICS='/rt/lowstate,/lio_sam/mapping/odometry,/lio_sam/localization/pose_in_map,/rt/odom,/rt/imu_debug' && source install/setup.bash && ros2 launch vita_flow flow.launch.py bag_path:=$(BAG_PATH)"
endif

# 裁剪mcap文件，支持传递mcap文件路径和起始时间参数
clip:
	$(eval ARGS := $(filter-out $@,$(MAKECMDGOALS)))
	$(eval MCAP_PATH := $(word 1,$(ARGS)))
	$(eval START_TIME := $(word 2,$(ARGS)))
	$(if $(MCAP_PATH),,$(error 错误: 需要提供mcap文件路径和起始时间。用法: make clip ~/Data/test.mcap 10.5))
	$(if $(START_TIME),,$(error 错误: 需要提供起始时间。用法: make clip ~/Data/test.mcap 10.5))
	$(eval MCAP_DIR := $(dir $(MCAP_PATH)))
	$(eval MCAP_FILENAME := $(notdir $(MCAP_PATH)))
	$(eval OUTPUT_PATH := $(MCAP_DIR)clipped_$(MCAP_FILENAME))
	@echo "裁剪mcap文件: $(MCAP_PATH)"
	@echo "起始时间: $(START_TIME)秒"
	@echo "输出文件: $(OUTPUT_PATH)"
	mcap filter $(MCAP_PATH) -S $(START_TIME) -o $(OUTPUT_PATH)
	@echo "裁剪完成: $(OUTPUT_PATH)"

# 编译并运行 himloco
himloco: msg
	colcon build --packages-select him_pkg \
		--parallel-workers 16 \
		--event-handlers console_direct+ && \
	bash -c "source install/setup.bash && \
		ros2 run him_pkg him_pkg_mt \
			--model_idx=Jul14_22-54-41_ckpt30000 \
			--runtime_env=realbot"
himloco_mujoco: msg
	colcon build --packages-select him_pkg \
		--parallel-workers 16 \
		--event-handlers console_direct+
	@if [ "$(OS)" = "Darwin" ]; then \
		zsh -c "source install/setup.zsh && \
			ros2 run him_pkg him_pkg_mt \
				--runtime_env=mujoco"; \
	else \
		bash -c "source install/setup.bash && \
			ros2 run him_pkg him_pkg_mt \
				--model_idx_a=Jul21_23-53-53_ckpt20000 \
				--model_idx_b=Jul29_16-30-13_ckpt20000 \
				--runtime_env=mujoco"; \
	fi

# 运行 virtual joystick
joystick:
ifeq ($(OS),Darwin)
	@echo "macOS 环境：使用 conda ros2 环境"
	zsh -c "source ~/miniconda3/bin/activate && conda activate ros2 && source install/setup.zsh && python3 src/unittest/joystick.py"
else
	bash -c "source $$(conda info --base)/etc/profile.d/conda.sh && conda activate himloco && source install/setup.bash && python3 src/unittest/joystick.py"
endif

# 速度控制命令
velocity_control:
	@echo "启动速度控制: 线速度=($(or $(x),0.0),$(or $(y),0.0)) 角速度=$(or $(z),0.0) 时长=$(or $(d),10.0)s"
ifeq ($(OS),Darwin)
	zsh -c "source ~/miniconda3/bin/activate && conda activate ros2 && source install/setup.zsh && python3 src/ros2_him/src/him_pkg/scripts/velocity_commander.py --velocity $(or $(x),0.0),$(or $(y),0.0),$(or $(z),0.0) --duration $(or $(d),10.0)"
else
	bash -c "source $$(conda info --base)/etc/profile.d/conda.sh && conda activate himloco && source install/setup.bash && python3 src/ros2_him/src/him_pkg/scripts/velocity_commander.py --velocity $(or $(x),0.0),$(or $(y),0.0),$(or $(z),0.0) --duration $(or $(d),10.0)"
endif

# 简单速度控制（向前移动）
velocity_forward:
	@echo "向前移动: 速度=$(or $(s),0.5)m/s 时长=$(or $(d),5.0)s"
	$(MAKE) velocity_control x=$(or $(s),0.5) d=$(or $(d),5.0)

# 原地转弯
velocity_turn:
	@echo "原地转弯: 角速度=$(or $(s),0.5)rad/s 时长=$(or $(d),5.0)s"
	$(MAKE) velocity_control z=$(or $(s),0.5) d=$(or $(d),5.0)

# 智能速度控制（带冲突检测）
velocity_smart:
	@echo "智能速度控制: 速度=$(or $(x),0.5)m/s 时长=$(or $(d),30.0)s"
	$(MAKE) velocity_control x=$(or $(x),0.5) d=$(or $(d),30.0) m=smart

arbitrator:
	bash -c "source $$(conda info --base)/etc/profile.d/conda.sh && conda activate himloco && source install/setup.bash && ros2 run him_pkg command_arbitrator"


# 更新子模块
submodule:
	git submodule update --init --recursive

# 启动 foxglove_bridge
bridge:
	bash -c "source install/setup.bash && ros2 run foxglove_bridge foxglove_bridge"

# macOS 进入虚拟 ros 环境
mac_env:
ifeq ($(OS),Darwin)
	@echo "进入 ROS2 conda 环境..."
	@zsh -c "source ~/miniconda3/bin/activate && conda activate ros2 && zsh"
else
	@echo "此命令仅适用于 macOS 系统"
endif

# 默认目标
.DEFAULT_GOAL := help

# 处理额外的参数（用于vitaflow）
%:
	@:
